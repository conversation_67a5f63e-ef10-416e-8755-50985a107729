<template>
  <full-screen-dialog
    :model-value="isOpen"
    @update:model-value="updateModelValue"
    @close="handleClose"
    title="Welcome to the Platform!"
    subtitle="Your account has been created successfully"
  >
    <div class="onboarding-content">
      <!-- Success Section -->
      <div class="success-section">
        <div class="success-icon">
          <unified-icon name="check_circle" size="4rem" color="primary" />
        </div>
        <h2 class="success-title">Account Created Successfully!</h2>
        <p class="success-message">
          Welcome to the platform! You're now part of our community as a 
          <strong>{{ selectedCategoryLabel }}</strong>.
        </p>
      </div>

      <!-- Next Steps Section -->
      <div class="next-steps-section">
        <div class="glass-container">
          <h3 class="section-title">What would you like to do next?</h3>
          
          <div class="options-grid">
            <!-- Continue with Profile Creation -->
            <div 
              class="option-card glass-card glass-card--interactive glass-card--primary"
              @click="handleContinueWithProfile"
              @keydown.enter="handleContinueWithProfile"
              @keydown.space.prevent="handleContinueWithProfile"
              tabindex="0"
              role="button"
              aria-label="Continue with profile creation"
            >
              <div class="option-card__content">
                <div class="option-card__icon">
                  <unified-icon name="person_add" size="3rem" color="primary" />
                </div>
                <div class="option-card__title">Continue with Profile Creation</div>
                <div class="option-card__description">
                  Complete your profile to get better connections and opportunities
                </div>
                <div class="option-card__badge">
                  <q-badge color="primary" label="Recommended" />
                </div>
              </div>
            </div>

            <!-- Do it Later -->
            <div 
              class="option-card glass-card glass-card--interactive"
              @click="handleDoItLater"
              @keydown.enter="handleDoItLater"
              @keydown.space.prevent="handleDoItLater"
              tabindex="0"
              role="button"
              aria-label="Skip profile creation for now"
            >
              <div class="option-card__content">
                <div class="option-card__icon">
                  <unified-icon name="dashboard" size="3rem" color="primary" />
                </div>
                <div class="option-card__title">Do it Later</div>
                <div class="option-card__description">
                  Go to your dashboard and explore the platform first
                </div>
                <div class="option-card__note">
                  You can complete your profile anytime from your dashboard
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Benefits Section -->
      <div class="benefits-section">
        <div class="glass-container">
          <h3 class="section-title">Why complete your profile?</h3>
          <div class="benefits-list">
            <div class="benefit-item">
              <unified-icon name="people" size="1.5rem" color="primary" />
              <span>Get matched with relevant connections</span>
            </div>
            <div class="benefit-item">
              <unified-icon name="business" size="1.5rem" color="primary" />
              <span>Discover opportunities tailored to your interests</span>
            </div>
            <div class="benefit-item">
              <unified-icon name="verified" size="1.5rem" color="primary" />
              <span>Build trust with a complete, verified profile</span>
            </div>
            <div class="benefit-item">
              <unified-icon name="trending_up" size="1.5rem" color="primary" />
              <span>Increase your visibility in the community</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </full-screen-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import FullScreenDialog from '@/components/ui/FullScreenDialog.vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import { useNotificationStore } from '@/stores/notifications'
import { useCategoryService } from '@/services/categoryService'

const router = useRouter()
const notifications = useNotificationStore()
const { state, closeAllDialogs } = useUnifiedAuth()
const { getCategoryById, getProfileTypeForCategory, clearSelectedCategory } = useCategoryService()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Computed properties
const isOpen = computed(() => state.value.isPostRegistrationOnboardingOpen)

const selectedCategoryLabel = computed(() => {
  const category = state.value.selectedCategory
  return category?.label || 'Member'
})

// Methods
const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  closeAllDialogs()
  emit('close')
}

const handleContinueWithProfile = async () => {
  try {
    const category = state.value.selectedCategory
    if (category) {
      // Navigate to profile creation with pre-selected category
      const profileType = getProfileTypeForCategory(category.id)
      
      closeAllDialogs()
      
      // Navigate to profile creation page
      await router.push({
        name: 'profile-create',
        query: {
          type: profileType,
          category: category.id
        }
      })
      
      notifications.success('Let\'s complete your profile to get the most out of the platform!')
    } else {
      // Fallback if no category is selected
      closeAllDialogs()
      await router.push({ name: 'profile-create' })
    }
  } catch (error) {
    console.error('Error navigating to profile creation:', error)
    notifications.error('Something went wrong. Please try again.')
  }
}

const handleDoItLater = async () => {
  try {
    closeAllDialogs()
    
    // Clear the selected category since user chose to skip
    clearSelectedCategory()
    
    // Navigate to dashboard
    await router.push('/dashboard')
    
    // Show success notification
    notifications.success('Welcome to the platform! You can complete your profile anytime from your dashboard.')
  } catch (error) {
    console.error('Error navigating to dashboard:', error)
    notifications.error('Something went wrong. Please try again.')
  }
}
</script>

<style scoped>
.onboarding-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  padding: 2rem 0;
  max-width: 1000px;
  margin: 0 auto;
}

.success-section {
  text-align: center;
  padding: 2rem 0;
}

.success-icon {
  margin-bottom: 1.5rem;
}

.success-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.success-message {
  font-size: 1.25rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
  max-width: 600px;
  margin: 0 auto;
}

.next-steps-section {
  flex: 1;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 2rem;
  text-align: center;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.option-card {
  padding: 2.5rem 2rem;
  text-align: center;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
}

.option-card__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  height: 100%;
}

.option-card__icon {
  margin-bottom: 0.5rem;
}

.option-card__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.option-card__description {
  font-size: 1rem;
  color: #666;
  line-height: 1.4;
  text-align: center;
  max-width: 280px;
}

.option-card__badge {
  margin-top: 1rem;
}

.option-card__note {
  font-size: 0.85rem;
  color: #888;
  font-style: italic;
  margin-top: 0.5rem;
}

.benefits-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1rem;
}

.benefits-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 1rem;
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .onboarding-content {
    gap: 2rem;
    padding: 1.5rem 0;
  }
  
  .success-title {
    font-size: 2rem;
  }
  
  .success-message {
    font-size: 1.1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .options-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .option-card {
    padding: 2rem 1.5rem;
    min-height: 250px;
  }
  
  .benefits-list {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .success-title {
    font-size: 1.75rem;
  }
  
  .success-message {
    font-size: 1rem;
  }
  
  .option-card {
    padding: 1.5rem 1rem;
    min-height: 220px;
  }
  
  .option-card__title {
    font-size: 1.25rem;
  }
  
  .option-card__description {
    font-size: 0.9rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .success-title,
  .section-title,
  .option-card__title {
    color: #000;
  }
  
  .success-message,
  .option-card__description,
  .benefit-item {
    color: #333;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .success-title,
  .section-title,
  .option-card__title {
    color: #f0f0f0;
  }
  
  .success-message,
  .option-card__description {
    color: #ccc;
  }
  
  .benefit-item {
    color: #ddd;
    background: rgba(255, 255, 255, 0.05);
  }
  
  .benefits-section {
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
